/**
 * Integration Tests for Document Creation Workflow
 * Tests complete document creation workflow from UI interaction to backend persistence
 * Verifies permission-based UI behavior and error handling
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useQuery, useMutation } from 'convex/react';
import { DocumentList } from '../../components/DocumentList';
import { useToast } from '../../hooks/use-toast';

// Mock the hooks
jest.mock('convex/react');
jest.mock('../../hooks/use-toast');

const mockUseQuery = useQuery;
const mockUseMutation = useMutation;
const mockUseToast = useToast;

describe('Document Creation Integration Tests', () => {
  const mockOnSelectDocument = jest.fn();
  const mockOnDocumentCountChange = jest.fn();
  const mockCreateDocument = jest.fn();
  const mockDeleteDocument = jest.fn();
  const mockToast = jest.fn();

  const defaultProps = {
    onSelectDocument: mockOnSelectDocument,
    selectedDocumentId: undefined,
    onDocumentCountChange: mockOnDocumentCountChange,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMutation.mockImplementation((mutation) => {
      if (mutation.toString().includes('createDocument')) {
        return mockCreateDocument;
      }
      if (mutation.toString().includes('deleteDocument')) {
        return mockDeleteDocument;
      }
      return jest.fn();
    });
    mockUseToast.mockReturnValue({ toast: mockToast });
  });

  describe('End-to-End Document Creation Workflow', () => {
    it('should complete full document creation workflow for authenticated user', async () => {
      const user = userEvent.setup();
      const newDocumentId = 'new-doc-123';
      
      // Mock initial state: empty document list, user can create
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { 
          canCreate: true, 
          reason: 'authenticated_user' 
        };
        return undefined;
      });

      mockCreateDocument.mockResolvedValue(newDocumentId);

      render(<DocumentList {...defaultProps} />);

      // Step 1: Verify initial state
      expect(screen.getByText('No documents yet')).toBeInTheDocument();
      expect(screen.getByText('Create your first document to get started.')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /new document/i })).toBeInTheDocument();

      // Step 2: Click create button
      await user.click(screen.getByRole('button', { name: /new document/i }));

      // Step 3: Verify dialog opens
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Create New Document')).toBeInTheDocument();

      // Step 4: Enter document title
      const titleInput = screen.getByPlaceholderText('Document title (optional)');
      await user.type(titleInput, 'My Integration Test Document');

      // Step 5: Submit form
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      // Step 6: Verify backend call
      await waitFor(() => {
        expect(mockCreateDocument).toHaveBeenCalledWith({
          title: 'My Integration Test Document'
        });
      });

      // Step 7: Verify success actions
      expect(mockOnSelectDocument).toHaveBeenCalledWith(newDocumentId);
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Document created',
        description: 'Your new document is ready to edit!',
      });

      // Step 8: Verify dialog closes
      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });

    it('should handle permission-based workflow for document owner', async () => {
      const user = userEvent.setup();
      const existingDocuments = [
        {
          _id: 'doc1',
          title: 'Existing Document',
          permission: 'owner',
          _creationTime: Date.now(),
          createdBy: 'user1',
          owner: { name: 'John Doe', email: '<EMAIL>' }
        }
      ];

      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return existingDocuments;
        if (query.toString().includes('canCreateDocuments')) return { 
          canCreate: true, 
          reason: 'document_owner' 
        };
        return undefined;
      });

      mockCreateDocument.mockResolvedValue('new-doc-456');

      render(<DocumentList {...defaultProps} />);

      // Verify user can create (has existing documents)
      expect(screen.getByRole('button', { name: /new document/i })).toBeInTheDocument();
      expect(screen.getByText('Existing Document')).toBeInTheDocument();
      expect(screen.getByText('owner')).toBeInTheDocument();

      // Complete creation workflow
      await user.click(screen.getByRole('button', { name: /new document/i }));
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      await waitFor(() => {
        expect(mockCreateDocument).toHaveBeenCalledWith({ title: undefined });
      });
    });

    it('should handle permission-based workflow for user with write access', async () => {
      const user = userEvent.setup();
      const sharedDocuments = [
        {
          _id: 'shared-doc1',
          title: 'Shared Document',
          permission: 'write',
          _creationTime: Date.now(),
          createdBy: 'other-user',
          owner: { name: 'Jane Smith', email: '<EMAIL>' }
        }
      ];

      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return sharedDocuments;
        if (query.toString().includes('canCreateDocuments')) return { 
          canCreate: true, 
          reason: 'has_write_permission' 
        };
        return undefined;
      });

      mockCreateDocument.mockResolvedValue('new-doc-789');

      render(<DocumentList {...defaultProps} />);

      // Verify user can create (has write permission)
      expect(screen.getByRole('button', { name: /new document/i })).toBeInTheDocument();
      expect(screen.getByText('Shared Document')).toBeInTheDocument();
      expect(screen.getByText('write')).toBeInTheDocument();
      expect(screen.getByText('Owned by Jane Smith')).toBeInTheDocument();

      // Complete creation workflow
      await user.click(screen.getByRole('button', { name: /new document/i }));
      await user.type(screen.getByPlaceholderText('Document title (optional)'), 'My Own Document');
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      await waitFor(() => {
        expect(mockCreateDocument).toHaveBeenCalledWith({ title: 'My Own Document' });
      });
    });
  });

  describe('Permission-Based UI Behavior', () => {
    it('should prevent document creation for anonymous users', async () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { 
          canCreate: false, 
          reason: 'anonymous_user' 
        };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      // Verify create button is not available
      expect(screen.queryByRole('button', { name: /new document/i })).not.toBeInTheDocument();

      // Verify warning message is shown
      expect(screen.getByText(/limited access/i)).toBeInTheDocument();
      expect(screen.getByText(/anonymous users can only view shared documents/i)).toBeInTheDocument();

      // Verify appropriate empty state message
      expect(screen.getByText('Sign up for an account or ask someone to share a document with you.')).toBeInTheDocument();
    });

    it('should show appropriate UI for unauthenticated users', async () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { 
          canCreate: false, 
          reason: 'not_authenticated' 
        };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.queryByRole('button', { name: /new document/i })).not.toBeInTheDocument();
      expect(screen.getByText(/please sign in to create and manage documents/i)).toBeInTheDocument();
    });

    it('should enable buttons and forms for users with read-only access', async () => {
      const readOnlyDocuments = [
        {
          _id: 'readonly-doc1',
          title: 'Read-only Document',
          permission: 'read',
          _creationTime: Date.now(),
          createdBy: 'other-user',
          owner: { name: 'Bob Wilson', email: '<EMAIL>' }
        }
      ];

      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return readOnlyDocuments;
        if (query.toString().includes('canCreateDocuments')) return { 
          canCreate: true, 
          reason: 'has_shared_access' 
        };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      // User with read access can still create their own documents
      expect(screen.getByRole('button', { name: /new document/i })).toBeInTheDocument();
      expect(screen.getByText('Read-only Document')).toBeInTheDocument();
      expect(screen.getByText('read')).toBeInTheDocument();
      expect(screen.queryByText(/limited access/i)).not.toBeInTheDocument();
    });
  });

  describe('Error Handling and User Feedback', () => {
    beforeEach(() => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { 
          canCreate: true, 
          reason: 'authenticated_user' 
        };
        return undefined;
      });
    });

    it('should handle backend permission errors during creation', async () => {
      const user = userEvent.setup();
      const permissionError = new Error('Anonymous users cannot create documents. Please sign up for an account.');
      mockCreateDocument.mockRejectedValue(permissionError);

      render(<DocumentList {...defaultProps} />);

      await user.click(screen.getByRole('button', { name: /new document/i }));
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Anonymous users cannot create documents. Please sign up for an account.',
          variant: 'destructive',
        });
      });
    });

    it('should handle network errors during creation', async () => {
      const user = userEvent.setup();
      const networkError = new Error('Network error');
      mockCreateDocument.mockRejectedValue(networkError);

      render(<DocumentList {...defaultProps} />);

      await user.click(screen.getByRole('button', { name: /new document/i }));
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Network error',
          variant: 'destructive',
        });
      });
    });

    it('should handle generic errors during creation', async () => {
      const user = userEvent.setup();
      mockCreateDocument.mockRejectedValue('Unknown error');

      render(<DocumentList {...defaultProps} />);

      await user.click(screen.getByRole('button', { name: /new document/i }));
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: 'Failed to create document',
          variant: 'destructive',
        });
      });
    });

    it('should show loading state during document creation', async () => {
      const user = userEvent.setup();
      let resolveCreate;
      const createPromise = new Promise(resolve => {
        resolveCreate = resolve;
      });
      mockCreateDocument.mockReturnValue(createPromise);

      render(<DocumentList {...defaultProps} />);

      await user.click(screen.getByRole('button', { name: /new document/i }));
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      // Verify loading state
      expect(screen.getByText('Creating...')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /creating/i })).toBeDisabled();

      // Resolve the promise
      resolveCreate('new-doc-id');
      
      await waitFor(() => {
        expect(screen.queryByText('Creating...')).not.toBeInTheDocument();
      });
    });
  });

  describe('Document Sharing Functionality', () => {
    it('should display shared documents with correct owner information', () => {
      const mixedDocuments = [
        {
          _id: 'owned-doc',
          title: 'My Document',
          permission: 'owner',
          _creationTime: Date.now(),
          createdBy: 'current-user',
          owner: { name: 'Current User', email: '<EMAIL>' }
        },
        {
          _id: 'shared-write-doc',
          title: 'Shared Write Document',
          permission: 'write',
          _creationTime: Date.now() - 1000,
          createdBy: 'other-user-1',
          owner: { name: 'Alice Johnson', email: '<EMAIL>' }
        },
        {
          _id: 'shared-read-doc',
          title: 'Shared Read Document',
          permission: 'read',
          _creationTime: Date.now() - 2000,
          createdBy: 'other-user-2',
          owner: { name: 'Charlie Brown', email: '<EMAIL>' }
        }
      ];

      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return mixedDocuments;
        if (query.toString().includes('canCreateDocuments')) return { 
          canCreate: true, 
          reason: 'document_owner' 
        };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      // Verify all documents are displayed
      expect(screen.getByText('My Document')).toBeInTheDocument();
      expect(screen.getByText('Shared Write Document')).toBeInTheDocument();
      expect(screen.getByText('Shared Read Document')).toBeInTheDocument();

      // Verify permission badges
      expect(screen.getByText('owner')).toBeInTheDocument();
      expect(screen.getByText('write')).toBeInTheDocument();
      expect(screen.getByText('read')).toBeInTheDocument();

      // Verify owner information is shown for shared documents only
      expect(screen.getByText('Owned by Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('Owned by Charlie Brown')).toBeInTheDocument();
      expect(screen.queryByText('Owned by Current User')).not.toBeInTheDocument();

      // Verify document count callback
      expect(mockOnDocumentCountChange).toHaveBeenCalledWith(3);
    });
  });
});
