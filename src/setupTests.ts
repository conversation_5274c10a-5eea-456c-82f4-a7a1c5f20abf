import '@testing-library/jest-dom';
import * as React from 'react';

// Mock Convex client
const mockConvexClient = {
  query: jest.fn(),
  mutation: jest.fn(),
  action: jest.fn(),
};

// Mock Convex React hooks
jest.mock('convex/react', () => ({
  useQuery: jest.fn(),
  useMutation: jest.fn(),
  useAction: jest.fn(),
  ConvexProvider: ({ children }: { children: React.ReactNode }) => children,
  ConvexReactClient: jest.fn(() => mockConvexClient),
}));

// Mock Convex auth
jest.mock('@convex-dev/auth/react', () => ({
  useAuthActions: jest.fn(() => ({
    signIn: jest.fn(),
    signOut: jest.fn(),
  })),
  Authenticated: ({ children }: { children: React.ReactNode }) => children,
  Unauthenticated: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock toast hook
jest.mock('./hooks/use-toast', () => ({
  useToast: jest.fn(() => ({
    toast: jest.fn(),
  })),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Plus: () => React.createElement('div', { 'data-testid': 'plus-icon' }),
  FileText: () => React.createElement('div', { 'data-testid': 'file-text-icon' }),
  Trash2: () => React.createElement('div', { 'data-testid': 'trash-icon' }),
  Crown: () => React.createElement('div', { 'data-testid': 'crown-icon' }),
  Eye: () => React.createElement('div', { 'data-testid': 'eye-icon' }),
  Edit: () => React.createElement('div', { 'data-testid': 'edit-icon' }),
  Lock: () => React.createElement('div', { 'data-testid': 'lock-icon' }),
  AlertCircle: () => React.createElement('div', { 'data-testid': 'alert-circle-icon' }),
}));

// Global test utilities
(global as any).mockConvexClient = mockConvexClient;

// Suppress console warnings in tests
const originalConsoleWarn = console.warn;
beforeAll(() => {
  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('React does not recognize')
    ) {
      return;
    }
    originalConsoleWarn(...args);
  };
});

afterAll(() => {
  console.warn = originalConsoleWarn;
});
