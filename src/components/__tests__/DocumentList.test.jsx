/**
 * Frontend Component Tests for DocumentList
 * Tests DocumentList component rendering for each permission level
 * Verifies correct UI elements are shown/hidden based on user permissions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useQuery, useMutation } from 'convex/react';
import { DocumentList } from '../DocumentList';
import { useToast } from '../../hooks/use-toast';

// Mock the hooks
jest.mock('convex/react');
jest.mock('../../hooks/use-toast');

const mockUseQuery = useQuery;
const mockUseMutation = useMutation;
const mockUseToast = useToast;

describe('DocumentList Component', () => {
  const mockOnSelectDocument = jest.fn();
  const mockOnDocumentCountChange = jest.fn();
  const mockCreateDocument = jest.fn();
  const mockDeleteDocument = jest.fn();
  const mockToast = jest.fn();

  const defaultProps = {
    onSelectDocument: mockOnSelectDocument,
    selectedDocumentId: undefined,
    onDocumentCountChange: mockOnDocumentCountChange,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMutation.mockImplementation((mutation) => {
      if (mutation.toString().includes('createDocument')) {
        return mockCreateDocument;
      }
      if (mutation.toString().includes('deleteDocument')) {
        return mockDeleteDocument;
      }
      return jest.fn();
    });
    mockUseToast.mockReturnValue({ toast: mockToast });
  });

  describe('Loading State', () => {
    it('should show loading spinner when documents are loading', () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return undefined;
        if (query.toString().includes('canCreateDocuments')) return { canCreate: true };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('Loading documents...')).toBeInTheDocument();
      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });
  });

  describe('Permission-Based UI Rendering', () => {
    const mockDocuments = [
      {
        _id: 'doc1',
        title: 'My Document',
        permission: 'owner',
        _creationTime: Date.now(),
        createdBy: 'user1',
        owner: { name: 'John Doe', email: '<EMAIL>' }
      },
      {
        _id: 'doc2',
        title: 'Shared Document',
        permission: 'read',
        _creationTime: Date.now() - 1000,
        createdBy: 'user2',
        owner: { name: 'Jane Smith', email: '<EMAIL>' }
      }
    ];

    it('should show create button for users with create permissions', () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return mockDocuments;
        if (query.toString().includes('canCreateDocuments')) return { canCreate: true, reason: 'authenticated_user' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByRole('button', { name: /new document/i })).toBeInTheDocument();
      expect(screen.queryByText(/limited access/i)).not.toBeInTheDocument();
    });

    it('should hide create button and show warning for anonymous users', () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { canCreate: false, reason: 'anonymous_user' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.queryByRole('button', { name: /new document/i })).not.toBeInTheDocument();
      expect(screen.getByText(/limited access/i)).toBeInTheDocument();
      expect(screen.getByText(/anonymous users can only view shared documents/i)).toBeInTheDocument();
    });

    it('should show appropriate message for unauthenticated users', () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { canCreate: false, reason: 'not_authenticated' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText(/please sign in to create and manage documents/i)).toBeInTheDocument();
    });

    it('should show generic message for other permission denials', () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { canCreate: false, reason: 'other_reason' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText(/you can view shared documents but cannot create new ones/i)).toBeInTheDocument();
    });
  });

  describe('Document List Rendering', () => {
    const mockDocuments = [
      {
        _id: 'doc1',
        title: 'My Document',
        permission: 'owner',
        _creationTime: Date.now(),
        createdBy: 'user1',
        owner: { name: 'John Doe', email: '<EMAIL>' }
      },
      {
        _id: 'doc2',
        title: 'Shared Document',
        permission: 'write',
        _creationTime: Date.now() - 1000,
        createdBy: 'user2',
        owner: { name: 'Jane Smith', email: '<EMAIL>' }
      },
      {
        _id: 'doc3',
        title: 'Read-only Document',
        permission: 'read',
        _creationTime: Date.now() - 2000,
        createdBy: 'user3',
        owner: { name: 'Bob Wilson', email: '<EMAIL>' }
      }
    ];

    beforeEach(() => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return mockDocuments;
        if (query.toString().includes('canCreateDocuments')) return { canCreate: true, reason: 'authenticated_user' };
        return undefined;
      });
    });

    it('should render all documents with correct permissions', () => {
      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('My Document')).toBeInTheDocument();
      expect(screen.getByText('Shared Document')).toBeInTheDocument();
      expect(screen.getByText('Read-only Document')).toBeInTheDocument();

      // Check permission badges
      expect(screen.getByText('owner')).toBeInTheDocument();
      expect(screen.getByText('write')).toBeInTheDocument();
      expect(screen.getByText('read')).toBeInTheDocument();
    });

    it('should show delete button only for owned documents', () => {
      render(<DocumentList {...defaultProps} />);

      const documentItems = screen.getAllByRole('generic').filter(el => 
        el.className.includes('group p-3 rounded-lg border cursor-pointer')
      );

      // Only the first document (owner) should have a delete button when hovered
      fireEvent.mouseEnter(documentItems[0]);
      expect(screen.getByTestId('trash-icon')).toBeInTheDocument();

      // Other documents should not have delete buttons
      fireEvent.mouseEnter(documentItems[1]);
      expect(screen.getAllByTestId('trash-icon')).toHaveLength(1); // Still only one
    });

    it('should show owner information for shared documents', () => {
      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('Owned by Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Owned by Bob Wilson')).toBeInTheDocument();
      expect(screen.queryByText('Owned by John Doe')).not.toBeInTheDocument(); // Owner's own document
    });

    it('should call onDocumentCountChange with correct count', () => {
      render(<DocumentList {...defaultProps} />);

      expect(mockOnDocumentCountChange).toHaveBeenCalledWith(3);
    });

    it('should highlight selected document', () => {
      render(<DocumentList {...defaultProps} selectedDocumentId="doc2" />);

      const selectedDocument = screen.getByText('Shared Document').closest('div');
      expect(selectedDocument).toHaveClass('border-blue-500', 'bg-blue-50');
    });
  });

  describe('Empty States', () => {
    it('should show appropriate empty state for users who can create documents', () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { canCreate: true, reason: 'authenticated_user' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('No documents yet')).toBeInTheDocument();
      expect(screen.getByText('Create your first document to get started.')).toBeInTheDocument();
    });

    it('should show appropriate empty state for anonymous users', () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { canCreate: false, reason: 'anonymous_user' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('Sign up for an account or ask someone to share a document with you.')).toBeInTheDocument();
    });

    it('should show appropriate empty state for users without create permissions', () => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { canCreate: false, reason: 'other_reason' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      expect(screen.getByText('Ask someone to share a document with you.')).toBeInTheDocument();
    });
  });

  describe('Document Creation', () => {
    beforeEach(() => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { canCreate: true, reason: 'authenticated_user' };
        return undefined;
      });
    });

    it('should open create dialog when new document button is clicked', async () => {
      const user = userEvent.setup();
      render(<DocumentList {...defaultProps} />);

      const createButton = screen.getByRole('button', { name: /new document/i });
      await user.click(createButton);

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Create New Document')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Document title (optional)')).toBeInTheDocument();
    });

    it('should create document with provided title', async () => {
      const user = userEvent.setup();
      mockCreateDocument.mockResolvedValue('new-doc-id');

      render(<DocumentList {...defaultProps} />);

      // Open dialog
      await user.click(screen.getByRole('button', { name: /new document/i }));

      // Enter title
      const titleInput = screen.getByPlaceholderText('Document title (optional)');
      await user.type(titleInput, 'My New Document');

      // Create document
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      await waitFor(() => {
        expect(mockCreateDocument).toHaveBeenCalledWith({
          title: 'My New Document'
        });
      });

      expect(mockOnSelectDocument).toHaveBeenCalledWith('new-doc-id');
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Document created',
        description: 'Your new document is ready to edit!',
      });
    });

    it('should create document with default title when none provided', async () => {
      const user = userEvent.setup();
      mockCreateDocument.mockResolvedValue('new-doc-id');

      render(<DocumentList {...defaultProps} />);

      await user.click(screen.getByRole('button', { name: /new document/i }));
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      await waitFor(() => {
        expect(mockCreateDocument).toHaveBeenCalledWith({
          title: undefined
        });
      });
    });

    it('should handle creation errors', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Failed to create document';
      mockCreateDocument.mockRejectedValue(new Error(errorMessage));

      render(<DocumentList {...defaultProps} />);

      await user.click(screen.getByRole('button', { name: /new document/i }));
      await user.click(screen.getByRole('button', { name: /^create$/i }));

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      });
    });

    it('should prevent creation for users without permissions', async () => {
      const user = userEvent.setup();
      
      // Mock user without create permissions
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return [];
        if (query.toString().includes('canCreateDocuments')) return { canCreate: false, reason: 'anonymous_user' };
        return undefined;
      });

      render(<DocumentList {...defaultProps} />);

      // Should not have create button
      expect(screen.queryByRole('button', { name: /new document/i })).not.toBeInTheDocument();
    });
  });

  describe('Document Interaction', () => {
    const mockDocuments = [
      {
        _id: 'doc1',
        title: 'My Document',
        permission: 'owner',
        _creationTime: Date.now(),
        createdBy: 'user1',
        owner: { name: 'John Doe', email: '<EMAIL>' }
      }
    ];

    beforeEach(() => {
      mockUseQuery.mockImplementation((query) => {
        if (query.toString().includes('getUserDocuments')) return mockDocuments;
        if (query.toString().includes('canCreateDocuments')) return { canCreate: true, reason: 'authenticated_user' };
        return undefined;
      });
    });

    it('should call onSelectDocument when document is clicked', async () => {
      const user = userEvent.setup();
      render(<DocumentList {...defaultProps} />);

      const documentItem = screen.getByText('My Document');
      await user.click(documentItem);

      expect(mockOnSelectDocument).toHaveBeenCalledWith('doc1');
    });
  });
});
